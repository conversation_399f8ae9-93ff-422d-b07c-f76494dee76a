<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <div class="box">
                <div class="leftBox">
                    <div class="boxItem" v-for="(item,index) in boxList" :key="index" @click="goDetail(item)">
                        <div class="topBox">
                            <div class="userAndtime">
                                <div class="userName">{{ item.name }}</div>
                                <div class="time">{{ item.createTime }}</div>
                            </div>
                            <div class="rightBtn">
                                <div class="btn">
                                    <img src="@/assets/dianzan_def.png" alt="" class="btnImg">
                                    <div class="number">{{ item.likeNum || '0' }}</div>
                                </div>
                                <div class="btn">
                                    <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                                    <div class="number">{{ item.plnm || '0'}}</div>
                                </div>
                                <div class="btn" @click="deleteTopic(item)">
                                    <img src="@/assets/delete.png" alt="" class="btnImg">
                                </div>
                            </div>
                        </div>
                        <div class="content">{{ item.title }}</div>
                        <div class="content">{{ item.content }}</div>
                        <div class="imgBox">
                            <div class="imgList" v-for="(imgItem,imgIndex) in item.imgList" :key="imgIndex">
                                <img :src="imgItem.img" alt="" class="imgSize">
                            </div>
                        </div>
                    </div>
                    <div v-if="boxList.length == 0" class="zantuneirong">
                        <img src="@/assets/zanwu.png" alt="">
                        <div class="text">暂无讨论~</div>
                    </div>
                </div>
                <div class="rightBox">
                    <div class="btnItem" @click="openTopicDialog">
                        <img src="@/assets/add_def.png" alt="" class="iconImg add-icon">
                        <span class="btnText">发布话题</span>
                    </div>
                    <div class="btnItem" @click="goRelease">
                        <img src="@/assets/send_def.png" alt="" class="iconImg send-icon">
                        <span class="btnText">我发布的</span>
                    </div>
                    <div class="btnItem" @click="goReply">
                        <img src="@/assets/ask_def.png" alt="" class="iconImg ask-icon">
                        <span class="btnText">我回复的</span>
                    </div>
                    <div class="btnItem" @click="goReplyMe">
                        <img src="@/assets/ques_def.png" alt="" class="iconImg ques-icon">
                        <span class="btnText">回复我的</span>
                    </div>
                </div>
            </div>
        </div>
        <el-pagination
            v-model:current-page="pageParams.pageNum"
            v-model:page-size="pageParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; justify-content: center;"
        />

        <DialogTopic ref="dialogTopicRef" @refresh="loadData" />
    </div>
</template>
    
<script setup>
import { useRouter, useRoute } from 'vue-router'
import DialogTopic from './dialogTopic.vue'
import { getMessageBoardList, deleteMessageBoard } from '@/api/study.js'

const route = useRoute()
const boxList = ref([]);
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
    type:2,
    researchOfficeId: route.query.id
})
const total = ref(0)
const router = useRouter()
const dialogTopicRef = ref()

const loadData = () => {
    getMessageBoardList(pageParams.value).then(res => {
        boxList.value = res.data
        total.value = res.page.total
    })
}

const goDetail = (item) => {
    router.push({
        path: '/jiaoyanshi/discussDetail',
        query: {
            id: item.id,
            researchOfficeId:item.researchOfficeId
        }
    })
}
const deleteTopic = (item) => {
    deleteMessageBoard({
        id: item.id
    }).then(res => {
        if (res.status == 0) {
            ElMessage.success('删除成功')
            loadData()
        }
    })
}

const goRelease = () => {
    router.push('/jiaoyanshi/release')
}

const goReply = () => {
    router.push({
        path: '/jiaoyanshi/myReply',
        query: {
            id: route.query.id
        }
    })
}

const goReplyMe = () => {
    router.push('/jiaoyanshi/replyMe')
}

const openTopicDialog = () => {
    dialogTopicRef.value.openDialog()
}


const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    loadData()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    loadData()
};

onMounted(() => {
    loadData()
});
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.zantuneirong{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 590px;
    background-color: #fff;
}
.box{
    display: flex;
    margin-top: 20px;
}
.leftBox{
    flex: 1;
    margin-right: 20px;
}
.rightBox{
    width: 236px;
    height: 300px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 30px;
}
.boxItem{
    width: 1024px;
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.btnItem{
    width: 176px;
    height: 48px;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 16px;
}
.btnItem:hover{
    background: #386CFC;
    transition: all 0.3s ease;
    .btnText{
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        transition: all 0.3s ease;
    }
    .add-icon{
        content: url('@/assets/add.png');
    }
    .send-icon{
        content: url('@/assets/send.png');
    }
    .ask-icon{
        content: url('@/assets/ask.png');
    }
    .ques-icon{
        content: url('@/assets/ques.png');
    }
}
.iconImg{
    width: 20px;
    height: 20px;
    margin-right: 8px;
}
.btnText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    transition: all 0.3s ease;
}
.text{
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #8a8c89;
}
</style>
  